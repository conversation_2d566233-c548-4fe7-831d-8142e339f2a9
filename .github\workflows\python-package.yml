# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Python package

on:
  push:
    tags:
      - '*released'
  workflow_dispatch:


jobs:

  update-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: master
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Update version.py
        run: |
          python update_version.py

      - name: Verify version.py
        run: |
          ls -l mineru/version.py
          cat mineru/version.py

      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "myhloli"
          git add mineru/version.py
          if git diff-index --quiet HEAD; then
            echo "No changes to commit"
          else
            git commit -m "Update version.py with new version"
          fi
        id: commit_changes

      - name: Push changes
        if: steps.commit_changes.outcome == 'success'
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}
        run: |
          git push origin HEAD:master

  check-install:
    needs: [ update-version ]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11", "3.12", "3.13"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: master
        fetch-depth: 0

    - name: Verify version.py
      run: |
        ls -l mineru/version.py
        cat mineru/version.py

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install mineru
      run: |
        python -m pip install --upgrade pip
        pip install -e .[core]

  build:
    needs: [ check-install ]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ "3.10"]

    steps:

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: master
        fetch-depth: 0

    - name: Install wheel
      run: |
        python -m pip install wheel
        pip install build

    - name: Build wheel
      run: |
        python -m build --wheel

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: wheel-file
        path: dist/*.whl
        retention-days: 30

  release:
    needs: [ build ]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: wheel-file
          path: dist

      - name: Create and Upload Release
        id: create_release
        uses: softprops/action-gh-release@4634c16e79c963813287e889244c50009e7f0981
        with:
          files: './dist/*.whl'
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}

      - name: Publish distribution to PyPI
        run: |
          pip install -U twine id keyring packaging readme-renderer requests requests-toolbelt rfc3986 rich urllib3
          twine check dist/*
          twine upload dist/* -u __token__ -p ${{ secrets.PYPI_TOKEN }}
